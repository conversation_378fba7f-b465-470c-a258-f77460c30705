use crate::config::PerformanceConfig;
use anyhow::Result;
use log::{debug, info, warn};
use std::collections::HashMap;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};

#[derive(Debug, <PERSON>lone)]
pub struct ConnectionStats {
    pub active_connections: usize,
    pub total_connections: usize,
    pub failed_connections: usize,
    pub bytes_transferred: u64,
}

#[derive(Debug)]
struct CacheEntry {
    data: Vec<u8>,
    created_at: Instant,
    access_count: AtomicUsize,
}

pub struct PerformanceManager {
    config: PerformanceConfig,
    active_connections: Arc<AtomicUsize>,
    total_connections: Arc<AtomicUsize>,
    failed_connections: Arc<AtomicUsize>,
    bytes_transferred: Arc<AtomicUsize>,
    cache: Arc<Mutex<HashMap<String, CacheEntry>>>,
    cache_size_bytes: Arc<AtomicUsize>,
}

impl PerformanceManager {
    pub fn new(config: PerformanceConfig) -> Self {
        info!("Performance manager initialized with {} max connections", config.max_connections);
        info!("Connection timeout: {}s, Keep-alive: {}s",
              config.connection_timeout_secs, config.keep_alive_timeout_secs);
        info!("Buffer size: {} bytes, Cache size: {} MB",
              config.buffer_size, config.cache_size_mb);

        Self {
            config,
            active_connections: Arc::new(AtomicUsize::new(0)),
            total_connections: Arc::new(AtomicUsize::new(0)),
            failed_connections: Arc::new(AtomicUsize::new(0)),
            bytes_transferred: Arc::new(AtomicUsize::new(0)),
            cache: Arc::new(Mutex::new(HashMap::new())),
            cache_size_bytes: Arc::new(AtomicUsize::new(0)),
        }
    }

    pub fn acquire_connection(&self) -> Result<ConnectionGuard> {
        let current = self.active_connections.load(Ordering::Relaxed);
        if current >= self.config.max_connections {
            anyhow::bail!("Maximum connections reached: {}", self.config.max_connections);
        }

        self.active_connections.fetch_add(1, Ordering::Relaxed);
        self.total_connections.fetch_add(1, Ordering::Relaxed);

        Ok(ConnectionGuard {
            active_connections: self.active_connections.clone(),
        })
    }

    pub fn record_connection_failure(&self) {
        self.failed_connections.fetch_add(1, Ordering::Relaxed);
    }

    pub fn record_bytes_transferred(&self, bytes: usize) {
        self.bytes_transferred.fetch_add(bytes, Ordering::Relaxed);
    }

    pub fn get_stats(&self) -> ConnectionStats {
        ConnectionStats {
            active_connections: self.active_connections.load(Ordering::Relaxed),
            total_connections: self.total_connections.load(Ordering::Relaxed),
            failed_connections: self.failed_connections.load(Ordering::Relaxed),
            bytes_transferred: self.bytes_transferred.load(Ordering::Relaxed) as u64,
        }
    }

    pub fn get_buffer_size(&self) -> usize {
        self.config.buffer_size
    }

    pub fn get_connection_timeout(&self) -> Duration {
        Duration::from_secs(self.config.connection_timeout_secs)
    }

    pub fn get_keep_alive_timeout(&self) -> Duration {
        Duration::from_secs(self.config.keep_alive_timeout_secs)
    }

    // Simple cache implementation for frequently accessed data
    pub fn cache_get(&self, key: &str) -> Option<Vec<u8>> {
        if let Ok(cache) = self.cache.lock() {
            if let Some(entry) = cache.get(key) {
                debug!("Cache hit for key: {}", key);
                Some(entry.data.clone())
            } else {
                debug!("Cache miss for key: {}", key);
                None
            }
        } else {
            None
        }
    }

    pub fn cache_put(&self, key: String, data: Vec<u8>) {
        let max_cache_size = self.config.cache_size_mb * 1024 * 1024;
        let data_size = data.len();

        // Simple cache eviction: if adding this entry would exceed the limit, clear cache
        if self.cache_size_bytes.load(Ordering::Relaxed) + data_size > max_cache_size {
            self.cache_clear();
        }

        if let Ok(mut cache) = self.cache.lock() {
            let entry = CacheEntry {
                data,
                created_at: Instant::now(),
                access_count: AtomicUsize::new(1),
            };

            cache.insert(key.clone(), entry);
            self.cache_size_bytes.fetch_add(data_size, Ordering::Relaxed);
            debug!("Cached data for key: {} ({} bytes)", key, data_size);
        }
    }

    pub fn cache_clear(&self) {
        let old_size = self.cache_size_bytes.swap(0, Ordering::Relaxed);
        if let Ok(mut cache) = self.cache.lock() {
            cache.clear();
        }
        info!("Cache cleared, freed {} bytes", old_size);
    }

    // Simplified cache cleanup
    pub fn cleanup_cache(&self) {
        let now = Instant::now();
        let mut removed_size = 0;
        let mut removed_count = 0;

        if let Ok(mut cache) = self.cache.lock() {
            cache.retain(|_key, entry| {
                // Remove entries older than 1 hour with low access count
                if now.duration_since(entry.created_at) > Duration::from_secs(3600)
                    && entry.access_count.load(Ordering::Relaxed) < 5 {
                    removed_size += entry.data.len();
                    removed_count += 1;
                    false
                } else {
                    true
                }
            });
        }

        if removed_count > 0 {
            self.cache_size_bytes.fetch_sub(removed_size, Ordering::Relaxed);
            info!("Cache cleanup: removed {} entries, freed {} bytes", removed_count, removed_size);
        }
    }
}

pub struct ConnectionGuard {
    active_connections: Arc<AtomicUsize>,
}

impl Drop for ConnectionGuard {
    fn drop(&mut self) {
        self.active_connections.fetch_sub(1, Ordering::Relaxed);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_connection_management() {
        let config = PerformanceConfig {
            max_connections: 2,
            connection_timeout_secs: 1,
            keep_alive_timeout_secs: 60,
            buffer_size: 8192,
            cache_size_mb: 10,
            worker_threads: None,
        };

        let manager = PerformanceManager::new(config);
        
        // Acquire first connection
        let _guard1 = manager.acquire_connection().await.unwrap();
        assert_eq!(manager.get_stats().active_connections, 1);
        
        // Acquire second connection
        let _guard2 = manager.acquire_connection().await.unwrap();
        assert_eq!(manager.get_stats().active_connections, 2);
        
        // Third connection should timeout
        let result = manager.acquire_connection().await;
        assert!(result.is_err());
    }

    #[test]
    fn test_cache_operations() {
        let config = PerformanceConfig {
            max_connections: 100,
            connection_timeout_secs: 30,
            keep_alive_timeout_secs: 60,
            buffer_size: 8192,
            cache_size_mb: 1, // 1MB cache
            worker_threads: None,
        };

        let manager = PerformanceManager::new(config);
        
        // Test cache miss
        assert!(manager.cache_get("test_key").is_none());
        
        // Test cache put and get
        let test_data = vec![1, 2, 3, 4, 5];
        manager.cache_put("test_key".to_string(), test_data.clone());
        assert_eq!(manager.cache_get("test_key"), Some(test_data));
        
        // Test cache clear
        manager.cache_clear();
        assert!(manager.cache_get("test_key").is_none());
    }
}
