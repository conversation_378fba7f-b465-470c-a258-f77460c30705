@echo off
echo Testing Rust Forward Proxy...

echo.
echo Checking if proxy is running...
netstat -an | findstr "8080.*LISTENING" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ HTTP proxy is listening on port 8080
) else (
    echo ✗ HTTP proxy is not running on port 8080
    goto :end
)

netstat -an | findstr "8443.*LISTENING" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✓ HTTPS proxy is listening on port 8443
) else (
    echo ✗ HTTPS proxy is not running on port 8443
)

echo.
echo Proxy Status: RUNNING
echo.
echo Configuration:
echo   HTTP Proxy:  127.0.0.1:8080
echo   HTTPS Proxy: 127.0.0.1:8443
echo   Certificate: ca/d9.fit/d9.pfx
echo   Domain: d9.fit
echo.
echo To use the proxy:
echo 1. Configure your browser proxy settings:
echo    - HTTP Proxy: 127.0.0.1:8080
echo    - HTTPS Proxy: 127.0.0.1:8443
echo.
echo 2. Or use with curl (if available):
echo    curl.exe -x http://127.0.0.1:8080 http://example.com
echo.
echo 3. To stop the proxy, press Ctrl+C in the proxy window

:end
pause
