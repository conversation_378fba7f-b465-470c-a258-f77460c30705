#[cfg(windows)]
use std::ffi::OsString;
#[cfg(windows)]
use std::sync::mpsc;
#[cfg(windows)]
use std::time::Duration;
#[cfg(windows)]
use windows_service::{
    define_windows_service,
    service::{
        ServiceControl, ServiceControlAccept, ServiceExitCode, ServiceState, ServiceStatus,
        ServiceType,
    },
    service_control_handler::{self, ServiceControlHandlerResult},
    service_dispatcher, Result,
};

use crate::config::ProxyConfig;
use crate::proxy::ProxyServer;
use log::{error, info, warn};
use std::sync::Arc;

// Windows服务名称和显示名称
pub const SERVICE_NAME: &str = "RustForwardProxy";
pub const SERVICE_DISPLAY_NAME: &str = "Rust Forward Proxy";
pub const SERVICE_DESCRIPTION: &str = "High-performance forward proxy server with HTTP/HTTPS support";

#[cfg(windows)]
define_windows_service!(ffi_service_main, service_main);

#[cfg(windows)]
pub fn run_service() -> Result<()> {
    // 注册服务入口点
    service_dispatcher::start(SERVICE_NAME, ffi_service_main)
}

#[cfg(windows)]
fn service_main(_arguments: Vec<OsString>) {
    if let Err(e) = run_service_impl() {
        error!("Service error: {}", e);
    }
}

#[cfg(windows)]
fn run_service_impl() -> windows_service::Result<()> {
    // 创建事件通道用于服务控制
    let (shutdown_tx, shutdown_rx) = mpsc::channel();

    // 定义服务控制处理器
    let event_handler = move |control_event| -> ServiceControlHandlerResult {
        match control_event {
            ServiceControl::Stop | ServiceControl::Shutdown => {
                info!("Service stop/shutdown requested");
                let _ = shutdown_tx.send(());
                ServiceControlHandlerResult::NoError
            }
            ServiceControl::Interrogate => ServiceControlHandlerResult::NoError,
            _ => ServiceControlHandlerResult::NotImplemented,
        }
    };

    // 注册服务控制处理器
    let status_handle = service_control_handler::register(SERVICE_NAME, event_handler)?;

    // 设置服务状态为启动中
    status_handle.set_service_status(ServiceStatus {
        service_type: ServiceType::OWN_PROCESS,
        current_state: ServiceState::StartPending,
        controls_accepted: ServiceControlAccept::empty(),
        exit_code: ServiceExitCode::Win32(0),
        checkpoint: 0,
        wait_hint: Duration::from_secs(3),
        process_id: None,
    })?;

    info!("Starting {} service", SERVICE_DISPLAY_NAME);
    info!("Service process ID: {}", std::process::id());
    info!("Service working directory: {}", std::env::current_dir().unwrap_or_default().display());

    // 启动代理服务器（移除未使用的运行时）

    // 在单独的任务中启动代理服务器
    let (proxy_tx, proxy_rx) = mpsc::channel();

    std::thread::spawn(move || {
        let rt = tokio::runtime::Runtime::new().unwrap();
        let result = rt.block_on(async {
            start_proxy_server().await
        });
        let _ = proxy_tx.send(result);

        // 保持运行时活跃
        rt.block_on(async {
            loop {
                tokio::time::sleep(Duration::from_secs(1)).await;
            }
        });
    });

    // 等待代理服务器启动结果
    let proxy_result = proxy_rx.recv().map_err(|_| {
        windows_service::Error::Winapi(std::io::Error::new(
            std::io::ErrorKind::Other,
            "Failed to receive proxy startup result",
        ))
    })?;

    match proxy_result {
        Ok(_proxy_server) => {
            // 设置服务状态为运行中
            status_handle.set_service_status(ServiceStatus {
                service_type: ServiceType::OWN_PROCESS,
                current_state: ServiceState::Running,
                controls_accepted: ServiceControlAccept::STOP | ServiceControlAccept::SHUTDOWN,
                exit_code: ServiceExitCode::Win32(0),
                checkpoint: 0,
                wait_hint: Duration::default(),
                process_id: None,
            })?;

            info!("{} service started successfully", SERVICE_DISPLAY_NAME);

            // 等待停止信号
            let _ = shutdown_rx.recv();

            info!("Service shutdown requested, stopping proxy server");
        }
        Err(e) => {
            error!("Failed to start proxy server: {}", e);
            
            // 设置服务状态为停止
            status_handle.set_service_status(ServiceStatus {
                service_type: ServiceType::OWN_PROCESS,
                current_state: ServiceState::Stopped,
                controls_accepted: ServiceControlAccept::empty(),
                exit_code: ServiceExitCode::Win32(1),
                checkpoint: 0,
                wait_hint: Duration::default(),
                process_id: None,
            })?;

            return Err(windows_service::Error::Winapi(std::io::Error::new(
                std::io::ErrorKind::Other,
                format!("Proxy server failed: {}", e),
            )));
        }
    }

    // 设置服务状态为停止中
    status_handle.set_service_status(ServiceStatus {
        service_type: ServiceType::OWN_PROCESS,
        current_state: ServiceState::StopPending,
        controls_accepted: ServiceControlAccept::empty(),
        exit_code: ServiceExitCode::Win32(0),
        checkpoint: 0,
        wait_hint: Duration::from_secs(5),
        process_id: None,
    })?;

    info!("{} service stopped", SERVICE_DISPLAY_NAME);

    // 设置服务状态为已停止
    status_handle.set_service_status(ServiceStatus {
        service_type: ServiceType::OWN_PROCESS,
        current_state: ServiceState::Stopped,
        controls_accepted: ServiceControlAccept::empty(),
        exit_code: ServiceExitCode::Win32(0),
        checkpoint: 0,
        wait_hint: Duration::default(),
        process_id: None,
    })?;

    Ok(())
}

// 启动代理服务器的异步函数
async fn start_proxy_server() -> anyhow::Result<Arc<ProxyServer>> {
    // 设置工作目录为可执行文件所在目录
    let exe_dir = get_exe_directory();
    info!("Service executable directory: {}", exe_dir);
    info!("Current working directory: {}", std::env::current_dir().unwrap_or_default().display());

    // 尝试设置工作目录
    if let Err(e) = std::env::set_current_dir(&exe_dir) {
        warn!("Failed to set working directory to {}: {}", exe_dir, e);
        warn!("Continuing with current directory, but config and certificate paths may need to be absolute");
    } else {
        info!("Working directory set to: {}", exe_dir);
    }

    // 查找配置文件
    let config_path = find_config_file();

    // 加载配置
    let mut config = if std::path::Path::new(&config_path).exists() {
        info!("Loading service configuration from: {}", config_path);
        ProxyConfig::from_file(&config_path)?
    } else {
        warn!("Config file {} not found, using default configuration", config_path);
        ProxyConfig::default()
    };

    // 修正配置中的相对路径为绝对路径
    fix_relative_paths(&mut config, &exe_dir);

    // 验证配置
    config.validate()?;

    // 创建并启动代理服务器
    let proxy_server = Arc::new(ProxyServer::new(config));

    // 启动HTTP和HTTPS服务器
    let http_server = proxy_server.clone();
    let https_server = proxy_server.clone();

    tokio::try_join!(
        async move { http_server.start_http_server().await },
        async move { https_server.start_https_server().await }
    )?;

    Ok(proxy_server)
}

// 修正配置中的相对路径
fn fix_relative_paths(config: &mut ProxyConfig, exe_dir: &str) {
    // 修正证书路径
    if !config.https.cert_path.starts_with("C:") && !config.https.cert_path.starts_with("\\\\") {
        let old_path = config.https.cert_path.clone();
        config.https.cert_path = format!("{}\\{}", exe_dir, old_path);
        info!("Fixed cert_path: {} -> {}", old_path, config.https.cert_path);
    }

    if !config.https.key_path.starts_with("C:") && !config.https.key_path.starts_with("\\\\") {
        let old_path = config.https.key_path.clone();
        config.https.key_path = format!("{}\\{}", exe_dir, old_path);
        info!("Fixed key_path: {} -> {}", old_path, config.https.key_path);
    }

    // 修正PFX路径（如果使用）
    if let Some(pfx_path) = &config.https.pfx_path {
        if !pfx_path.starts_with("C:") && !pfx_path.starts_with("\\\\") {
            let old_path = pfx_path.clone();
            config.https.pfx_path = Some(format!("{}\\{}", exe_dir, old_path));
            info!("Fixed pfx_path: {} -> {}", old_path, config.https.pfx_path.as_ref().unwrap());
        }
    }
}

// 查找配置文件
fn find_config_file() -> String {
    // 获取可执行文件所在目录
    let exe_dir = get_exe_directory();

    // 服务模式下的配置文件查找顺序
    let possible_paths = vec![
        // 1. 可执行文件同目录下的配置文件
        format!("{}\\config.ini", exe_dir),
        format!("{}\\proxy.ini", exe_dir),
        // 2. 当前工作目录（可能是System32）
        "config.ini".to_string(),
        "proxy.ini".to_string(),
        // 3. 标准系统位置
        r"C:\ProgramData\RustForwardProxy\config.ini".to_string(),
        r"C:\Program Files\RustForwardProxy\config.ini".to_string(),
    ];

    info!("Service searching for config file in the following locations:");
    for (i, path) in possible_paths.iter().enumerate() {
        info!("  {}: {}", i + 1, path);
        if std::path::Path::new(path).exists() {
            info!("✓ Found config file: {}", path);
            return path.to_string();
        }
    }

    // 默认返回可执行文件目录下的config.ini
    let default_config = format!("{}\\config.ini", exe_dir);
    warn!("No config file found, using default: {}", default_config);
    default_config
}

// 获取可执行文件所在目录
fn get_exe_directory() -> String {
    match std::env::current_exe() {
        Ok(exe_path) => {
            if let Some(parent) = exe_path.parent() {
                parent.to_string_lossy().to_string()
            } else {
                ".".to_string()
            }
        }
        Err(e) => {
            error!("Failed to get executable path: {}", e);
            ".".to_string()
        }
    }
}

// 非Windows平台的占位符实现
#[cfg(not(windows))]
pub fn run_service() -> Result<(), Box<dyn std::error::Error>> {
    Err("Windows service is only supported on Windows platform".into())
}
