[package]
name = "rust-forward-proxy"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A high-performance forward proxy server supporting HTTP and HTTPS"
license = "MIT"

[dependencies]
# Async runtime (modern version compatible with Rust 1.75)
tokio = { version = "1.35", features = ["full"] }

# HTTP/HTTPS support (modern versions)
hyper = { version = "0.14", features = ["full"] }
hyper-tls = "0.5"

# TLS/SSL support (versions compatible with Rust 1.75)
rustls = "0.21.10"
rustls-pemfile = "1.0.4"
tokio-rustls = "0.24.1"

# Note: native-tls removed - using rustls for all TLS operations

# Configuration parsing
ini = "1.3"
serde = { version = "1.0", features = ["derive"] }

# Logging (versions from 2023 era)
log = "0.4.20"
env_logger = "0.10.1"

# Error handling
anyhow = "1.0.75"
thiserror = "1.0.50"

# Base64 for authentication
base64 = "0.21.5"

# Async utilities
futures = "0.3.29"
bytes = "1.5.0"

# Connection pooling and performance
dashmap = "5.5.3"
arc-swap = "1.6.0"

# Time utilities
chrono = { version = "0.4", features = ["serde"] }

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
