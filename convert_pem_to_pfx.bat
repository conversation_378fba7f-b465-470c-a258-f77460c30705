@echo off
echo Converting PEM certificates to PFX format...

echo.
echo Checking for OpenSSL...
where openssl >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo OpenSSL not found in PATH. Trying Git Bash...
    if exist "C:\Program Files\Git\usr\bin\openssl.exe" (
        set OPENSSL="C:\Program Files\Git\usr\bin\openssl.exe"
        echo Found OpenSSL in Git Bash
    ) else (
        echo ERROR: OpenSSL not found
        echo Please install Git for Windows or OpenSSL
        echo Or use the existing d9.pfx file
        pause
        exit /b 1
    )
) else (
    set OPENSSL=openssl
    echo Found OpenSSL in PATH
)

echo.
echo Converting certificates...
%OPENSSL% pkcs12 -export -out ca\d9.fit\converted.pfx -inkey ca\d9.fit\privkey.pem -in ca\d9.fit\fullchain.pem -passout pass:

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: Created ca\d9.fit\converted.pfx
    echo.
    echo Update your proxy.ini to use:
    echo   pfx_path = ./ca/d9.fit/converted.pfx
    echo   pfx_password = 
) else (
    echo.
    echo FAILED: Could not convert certificates
    echo Please use the existing d9.pfx file instead
)

echo.
pause
