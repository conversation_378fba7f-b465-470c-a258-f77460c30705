{"rustc_fingerprint": 5781998326506686298, "outputs": {"3451159644850784790": {"success": true, "status": "", "code": 0, "stdout": "___.exe\nlib___.rlib\n___.dll\n___.dll\n___.lib\n___.dll\nC:\\Program Files (x86)\\Rust stable MSVC 1.75\npacked\n___\ndebug_assertions\npanic=\"unwind\"\nproc_macro\ntarget_arch=\"x86\"\ntarget_endian=\"little\"\ntarget_env=\"msvc\"\ntarget_family=\"windows\"\ntarget_feature=\"fxsr\"\ntarget_feature=\"sse\"\ntarget_feature=\"sse2\"\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_os=\"windows\"\ntarget_pointer_width=\"32\"\ntarget_vendor=\"pc\"\nwindows\n", "stderr": ""}, "15872395580024362796": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.75.0 (82e1608df 2023-12-21)\nbinary: rustc\ncommit-hash: 82e1608dfa6e0b5569232559e3d385fea5a93112\ncommit-date: 2023-12-21\nhost: i686-pc-windows-msvc\nrelease: 1.75.0\nLLVM version: 17.0.6\n", "stderr": ""}}, "successes": {}}