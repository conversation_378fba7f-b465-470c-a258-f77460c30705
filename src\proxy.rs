use crate::auth::AuthManager;
use crate::config::ProxyConfig;
use crate::tls::TlsManager;
use anyhow::{Context, Result};
use hyper::service::{make_service_fn, service_fn};
use hyper::upgrade::Upgraded;
use hyper::{Body, Client, Method, Request, Response, Server, StatusCode};
use log::{debug, error, info, warn};
use std::convert::Infallible;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::io::{copy_bidirectional, AsyncReadExt, AsyncWriteExt};
use tokio::net::{TcpListener, TcpStream};
use tokio::time::{timeout, Duration};


#[derive(Clone)]
pub struct ProxyServer {
    pub config: Arc<ProxyConfig>,
    auth_manager: Arc<AuthManager>,
    client: Client<hyper::client::HttpConnector>,
}

impl ProxyServer {
    pub fn new(config: ProxyConfig) -> Self {
        let auth_manager = Arc::new(AuthManager::new(
            config.auth.enabled,
            config.auth.users.clone(),
            config.auth.realm.clone(),
        ));

        let client = Client::builder()
            .pool_idle_timeout(Duration::from_secs(config.performance.keep_alive_timeout_secs))
            .pool_max_idle_per_host(config.performance.max_connections / 10)
            .build_http();

        Self {
            config: Arc::new(config),
            auth_manager,
            client,
        }
    }

    pub async fn start_http_server(&self) -> Result<()> {
        if !self.config.http.enabled {
            info!("HTTP proxy is disabled");
            return Ok(());
        }

        let addr: SocketAddr = format!("{}:{}", self.config.http.bind_address, self.config.http.port)
            .parse()
            .context("Invalid HTTP bind address")?;

        let proxy_server = self.clone();
        let make_svc = make_service_fn(move |_conn| {
            let proxy_server = proxy_server.clone();
            async move {
                Ok::<_, Infallible>(service_fn(move |req| {
                    let proxy_server = proxy_server.clone();
                    async move { proxy_server.handle_request(req).await }
                }))
            }
        });

        let server = Server::bind(&addr)
            .serve(make_svc)
            .with_graceful_shutdown(shutdown_signal());

        info!("HTTP proxy server listening on {}", addr);
        server.await.context("HTTP server error")?;

        Ok(())
    }

    pub async fn start_https_server(&self) -> Result<()> {
        if !self.config.https.enabled {
            info!("HTTPS proxy is disabled");
            return Ok(());
        }

        let addr: SocketAddr = format!("{}:{}", self.config.https.bind_address, self.config.https.port)
            .parse()
            .context("Invalid HTTPS bind address")?;

        let tls_manager = TlsManager::new(self.config.https.clone());
        tls_manager.validate_certificates()?;

        let tls_acceptor = match tls_manager.create_acceptor().await {
            Ok(acceptor) => acceptor,
            Err(e) => {
                error!("Failed to create TLS acceptor: {}", e);
                warn!("HTTPS proxy disabled due to TLS configuration error");
                warn!("Please check your certificate configuration");
                return Ok(());
            }
        };

        let listener = TcpListener::bind(&addr).await
            .context("Failed to bind HTTPS listener")?;

        info!("HTTPS proxy server listening on {}", addr);

        let proxy_server = self.clone();
        loop {
            let (stream, peer_addr) = listener.accept().await?;
            let tls_acceptor = tls_acceptor.clone();
            let proxy_server = proxy_server.clone();

            tokio::spawn(async move {
                match tls_acceptor.accept(stream).await {
                    Ok(tls_stream) => {
                        debug!("TLS connection established from {}", peer_addr);
                        if let Err(e) = proxy_server.handle_https_connection(tls_stream).await {
                            error!("HTTPS connection error for {}: {}", peer_addr, e);
                        }
                    }
                    Err(e) => {
                        // More detailed TLS error logging
                        let error_msg = format!("{}", e);
                        if error_msg.contains("eof") {
                            debug!("TLS handshake EOF from {}: client disconnected early", peer_addr);
                        } else if error_msg.contains("protocol") {
                            warn!("TLS protocol error from {}: {}", peer_addr, e);
                        } else if error_msg.contains("timeout") {
                            debug!("TLS handshake timeout from {}", peer_addr);
                        } else {
                            error!("TLS handshake failed for {}: {}", peer_addr, e);
                        }
                    }
                }
            });
        }
    }

    async fn handle_https_connection(&self, mut stream: tokio_rustls::server::TlsStream<TcpStream>) -> Result<()> {
        let mut buffer = [0; 4096];

        // Read the HTTP request
        let n = stream.read(&mut buffer).await?;
        if n == 0 {
            return Ok(());
        }

        let request_str = String::from_utf8_lossy(&buffer[..n]);
        let lines: Vec<&str> = request_str.lines().collect();

        if lines.is_empty() {
            return Ok(());
        }

        let request_line = lines[0];
        let parts: Vec<&str> = request_line.split_whitespace().collect();

        if parts.len() < 3 {
            return Ok(());
        }

        let method = parts[0];
        let uri = parts[1];

        info!("HTTPS request: {} {}", method, uri);

        // Check authentication
        let mut authenticated = false;
        if self.auth_manager.is_enabled() {
            for line in &lines {
                if line.starts_with("Proxy-Authorization:") {
                    if let Some(auth_header) = line.strip_prefix("Proxy-Authorization:") {
                        if self.auth_manager.authenticate_header(auth_header.trim()) {
                            authenticated = true;
                            break;
                        }
                    }
                }
            }

            if !authenticated {
                let response = "HTTP/1.1 407 Proxy Authentication Required\r\nProxy-Authenticate: Basic realm=\"Proxy\"\r\nContent-Length: 0\r\n\r\n";
                stream.write_all(response.as_bytes()).await?;
                return Ok(());
            }
        } else {
            authenticated = true;
        }

        if method == "CONNECT" && authenticated {
            self.handle_https_connect(stream, uri).await
        } else if authenticated {
            // For non-CONNECT requests (GET, POST, etc.), handle them directly
            self.handle_https_http_request(stream, method, uri, &lines).await
        } else {
            // Authentication failed, already sent 407 response
            Ok(())
        }
    }

    async fn handle_https_connect(&self, mut client_stream: tokio_rustls::server::TlsStream<TcpStream>, uri: &str) -> Result<()> {
        info!("Handling HTTPS CONNECT request to: {}", uri);

        // Parse host and port
        let (host, port) = if let Some((h, p)) = uri.split_once(':') {
            (h.to_string(), p.parse::<u16>().unwrap_or(443))
        } else {
            (uri.to_string(), 443)
        };

        // Connect to target server
        match timeout(
            Duration::from_secs(self.config.performance.connection_timeout_secs),
            TcpStream::connect((host.as_str(), port))
        ).await {
            Ok(Ok(mut target_stream)) => {
                info!("Successfully connected to target: {}:{}", host, port);

                // Send 200 Connection Established
                let response = "HTTP/1.1 200 Connection Established\r\n\r\n";
                if let Err(e) = client_stream.write_all(response.as_bytes()).await {
                    error!("Failed to send CONNECT response: {}", e);
                    return Ok(());
                }

                // Start bidirectional copying
                match copy_bidirectional(&mut client_stream, &mut target_stream).await {
                    Ok((client_to_target, target_to_client)) => {
                        info!("HTTPS tunnel closed for {}:{} - {} bytes up, {} bytes down",
                              host, port, client_to_target, target_to_client);
                    }
                    Err(e) => {
                        debug!("HTTPS tunnel error for {}:{}: {}", host, port, e);
                    }
                }
            }
            Ok(Err(e)) => {
                error!("Failed to connect to target {}:{}: {}", host, port, e);
                let response = "HTTP/1.1 502 Bad Gateway\r\n\r\n";
                let _ = client_stream.write_all(response.as_bytes()).await;
            }
            Err(_) => {
                error!("Connection timeout to {}:{}", host, port);
                let response = "HTTP/1.1 504 Gateway Timeout\r\n\r\n";
                let _ = client_stream.write_all(response.as_bytes()).await;
            }
        }

        Ok(())
    }

    async fn handle_https_http_request(&self, mut stream: tokio_rustls::server::TlsStream<TcpStream>,
                                      method: &str, uri: &str, lines: &[&str]) -> Result<()> {
        info!("Handling HTTPS HTTP request: {} {}", method, uri);

        // For HTTP requests through HTTPS proxy, we need to forward them to the target server
        // Parse the full URL
        let url = if uri.starts_with("http://") || uri.starts_with("https://") {
            uri.to_string()
        } else {
            // Extract host from headers if available
            let host = lines.iter()
                .find(|line| line.to_lowercase().starts_with("host:"))
                .and_then(|line| line.split_once(':'))
                .map(|(_, host)| host.trim())
                .unwrap_or(uri);

            if uri.starts_with('/') {
                format!("http://{}{}", host, uri)
            } else {
                format!("http://{}/{}", host, uri)
            }
        };

        debug!("Forwarding HTTP request to: {}", url);

        // Use hyper client to make the request
        let client = Client::builder()
            .pool_idle_timeout(Duration::from_secs(30))
            .pool_max_idle_per_host(10)
            .build_http();

        // Build the request
        let mut request_builder = hyper::Request::builder()
            .method(method)
            .uri(&url);

        // Copy headers from original request
        for line in lines {
            if let Some((name, value)) = line.split_once(':') {
                let name = name.trim();
                let value = value.trim();
                if !name.is_empty() && !value.is_empty() &&
                   !name.eq_ignore_ascii_case("proxy-authorization") &&
                   !name.eq_ignore_ascii_case("connection") &&
                   !name.eq_ignore_ascii_case("host") {
                    // Safely try to add header
                    match (hyper::header::HeaderName::from_bytes(name.as_bytes()),
                           hyper::header::HeaderValue::from_str(value)) {
                        (Ok(header_name), Ok(header_value)) => {
                            request_builder = request_builder.header(header_name, header_value);
                        }
                        _ => {
                            debug!("Skipping invalid header: {} = {}", name, value);
                        }
                    }
                }
            }
        }

        let request = match request_builder.body(Body::empty()) {
            Ok(req) => req,
            Err(e) => {
                error!("Failed to build HTTP request: {}", e);
                let error_response = "HTTP/1.1 400 Bad Request\r\n\r\n";
                stream.write_all(error_response.as_bytes()).await?;
                return Ok(());
            }
        };

        // Make the request
        match client.request(request).await {
            Ok(response) => {
                let status = response.status();
                let headers = response.headers().clone();

                info!("Received response: {} for {} {}", status, method, uri);

                // Send response status line
                let status_line = format!("HTTP/1.1 {} {}\r\n", status.as_u16(),
                                        status.canonical_reason().unwrap_or(""));
                stream.write_all(status_line.as_bytes()).await?;

                // Read body first
                let body_bytes = hyper::body::to_bytes(response.into_body()).await?;
                let body_len = body_bytes.len();

                debug!("Response body length: {} bytes", body_len);

                // Send headers (update Content-Length if needed)
                let mut sent_content_length = false;
                for (name, value) in &headers {
                    if name.as_str().eq_ignore_ascii_case("content-length") {
                        let header_line = format!("Content-Length: {}\r\n", body_len);
                        stream.write_all(header_line.as_bytes()).await?;
                        sent_content_length = true;
                    } else if !name.as_str().eq_ignore_ascii_case("transfer-encoding") {
                        let header_line = format!("{}: {}\r\n", name, value.to_str().unwrap_or(""));
                        stream.write_all(header_line.as_bytes()).await?;
                    }
                }

                // Add Content-Length if not present
                if !sent_content_length {
                    let header_line = format!("Content-Length: {}\r\n", body_len);
                    stream.write_all(header_line.as_bytes()).await?;
                }

                // End headers
                stream.write_all(b"\r\n").await?;

                // Send body
                if body_len > 0 {
                    stream.write_all(&body_bytes).await?;
                    debug!("Sent {} bytes of response body", body_len);
                } else {
                    debug!("Empty response body");
                }

                // Ensure data is flushed
                stream.flush().await?;

                info!("HTTPS HTTP request completed: {} {} - {} bytes", method, uri, body_len);
            }
            Err(e) => {
                error!("Failed to process HTTPS HTTP request: {}", e);
                let error_response = "HTTP/1.1 502 Bad Gateway\r\nContent-Length: 0\r\n\r\n";
                stream.write_all(error_response.as_bytes()).await?;
                stream.flush().await?;
            }
        }

        Ok(())
    }

    async fn handle_request(&self, req: Request<Body>) -> Result<Response<Body>, Infallible> {
        let start_time = std::time::Instant::now();
        let method = req.method().clone();
        let uri = req.uri().clone();

        info!("Incoming request: {} {}", method, uri);

        // Check authentication
        if !self.auth_manager.authenticate(req.headers()) {
            warn!("Authentication failed for request: {} {}", method, uri);
            let (status, headers) = self.auth_manager.get_auth_challenge();
            let mut response = Response::new(Body::from("Proxy Authentication Required"));
            *response.status_mut() = status;
            *response.headers_mut() = headers;
            return Ok(response);
        }

        let result = match req.method() {
            &Method::CONNECT => {
                info!("Handling CONNECT request");
                self.handle_connect(req).await
            },
            _ => {
                info!("Handling HTTP request");
                self.handle_http_request(req).await
            },
        };

        let duration = start_time.elapsed();
        debug!("Request processed in {:?}", duration);

        match result {
            Ok(response) => {
                info!("Request completed successfully: {} {}", method, uri);
                Ok(response)
            },
            Err(e) => {
                error!("Request handling error for {} {}: {}", method, uri, e);
                Ok(Response::builder()
                    .status(StatusCode::INTERNAL_SERVER_ERROR)
                    .body(Body::from(format!("Internal Server Error: {}", e)))
                    .unwrap())
            }
        }
    }

    async fn handle_connect(&self, mut req: Request<Body>) -> Result<Response<Body>> {
        let uri_str = req.uri().to_string();
        debug!("Handling HTTP CONNECT request to: {}", uri_str);

        let authority = req.uri().authority()
            .ok_or_else(|| anyhow::anyhow!("CONNECT request missing authority"))?
            .as_str()
            .to_string();

        // Parse host and port
        let (host, port) = if let Some((h, p)) = authority.split_once(':') {
            (h.to_string(), p.parse::<u16>().unwrap_or(443))
        } else {
            (authority.clone(), 443)
        };

        // Connect to target server with timeout
        let target_stream = timeout(
            Duration::from_secs(self.config.performance.connection_timeout_secs),
            TcpStream::connect((host.as_str(), port))
        ).await
        .context("Connection timeout")?
        .context("Failed to connect to target")?;

        info!("Successfully connected to target: {}:{}", host, port);

        // Spawn the tunnel task for HTTP proxy CONNECT
        tokio::spawn(async move {
            match hyper::upgrade::on(&mut req).await {
                Ok(upgraded) => {
                    info!("HTTP connection upgraded, starting tunnel to {}:{}", host, port);
                    if let Err(e) = tunnel_data(upgraded, target_stream).await {
                        error!("HTTP tunnel error for {}:{}: {}", host, port, e);
                    } else {
                        info!("HTTP tunnel closed for {}:{}", host, port);
                    }
                }
                Err(e) => {
                    error!("Failed to upgrade HTTP connection for {}:{}: {}", host, port, e);
                }
            }
        });

        // Return 200 Connection Established
        Ok(Response::builder()
            .status(StatusCode::OK)
            .body(Body::empty())?)
    }

    async fn handle_http_request(&self, mut req: Request<Body>) -> Result<Response<Body>> {
        debug!("Handling HTTP request to: {}", req.uri());

        // Remove proxy-specific headers
        req.headers_mut().remove("proxy-connection");
        req.headers_mut().remove("proxy-authorization");

        // Forward the request
        let response = timeout(
            Duration::from_secs(self.config.performance.connection_timeout_secs),
            self.client.request(req)
        ).await
        .context("Request timeout")?
        .context("Failed to forward request")?;

        Ok(response)
    }
}

// Bidirectional data transfer for CONNECT tunnels
async fn tunnel_data(upgraded: Upgraded, target: TcpStream) -> Result<()> {
    let (mut client_read, mut client_write) = tokio::io::split(upgraded);
    let (mut target_read, mut target_write) = target.into_split();

    let client_to_target = async {
        match tokio::io::copy(&mut client_read, &mut target_write).await {
            Ok(bytes) => {
                debug!("Client to target: {} bytes transferred", bytes);
                let _ = target_write.shutdown().await;
                Ok(())
            }
            Err(e) => {
                debug!("Client to target transfer error: {}", e);
                Err(e)
            }
        }
    };

    let target_to_client = async {
        match tokio::io::copy(&mut target_read, &mut client_write).await {
            Ok(bytes) => {
                debug!("Target to client: {} bytes transferred", bytes);
                let _ = client_write.shutdown().await;
                Ok(())
            }
            Err(e) => {
                debug!("Target to client transfer error: {}", e);
                Err(e)
            }
        }
    };

    // Run both transfers concurrently until one completes
    tokio::select! {
        result = client_to_target => {
            debug!("Client to target transfer completed: {:?}", result);
        }
        result = target_to_client => {
            debug!("Target to client transfer completed: {:?}", result);
        }
    }

    Ok(())
}

async fn shutdown_signal() {
    tokio::signal::ctrl_c()
        .await
        .expect("Failed to install CTRL+C signal handler");
    info!("Shutdown signal received");
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::ProxyConfig;

    #[tokio::test]
    async fn test_proxy_server_creation() {
        let config = ProxyConfig::default();
        let proxy = ProxyServer::new(config);
        assert!(!proxy.auth_manager.is_enabled());
    }
}
