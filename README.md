# Rust Forward Proxy

一个高性能的Rust正向代理服务器，支持HTTP和HTTPS协议，具有用户认证、TLS支持和性能优化功能。

## 功能特性

- ✅ **HTTP/HTTPS代理**: 支持HTTP和HTTPS正向代理
- ✅ **用户认证**: 基于用户名密码的代理认证
- ✅ **TLS支持**: 支持自定义证书和Let's Encrypt自动证书管理
- ✅ **高性能**: 连接池、缓存、异步I/O优化
- ✅ **配置灵活**: INI格式配置文件，支持热配置
- ✅ **监控统计**: 连接统计和性能监控

## 系统要求

- **Rust 1.75** (推荐 - 支持Windows 7的最后版本)
- Visual Studio 2022 (Windows)
- 您的Let's Encrypt证书文件 (已包含在ca/d9.fit目录中)

## 快速开始

### 0. 升级Rust版本（重要！）

如果您当前使用的是Rust 1.59，需要升级到1.75以获得完整的HTTPS支持：

```bash
# 运行升级脚本
.\upgrade_rust.bat

# 或手动升级
rustup install 1.75
rustup default 1.75
```

### 1. 编译项目

```bash
cargo build --release
```

### 2. 配置代理

项目已经预配置使用您的d9.fit证书：

```ini
[https]
enabled = true
port = 8443
cert_path = ./ca/d9.fit/fullchain.pem
key_path = ./ca/d9.fit/privkey.pem
pfx_path = ./ca/d9.fit/d9.pfx
```

如需自定义配置，复制并编辑配置文件：

```bash
copy config.ini my-config.ini
```

### 3. 运行代理服务器

```bash
# 使用默认配置文件 config.ini
cargo run --release

# 或指定配置文件
cargo run --release my-config.ini
cargo run --release proxy.ini  # 兼容旧配置文件
```

## 配置说明

### HTTP配置

```ini
[http]
enabled = true          # 启用HTTP代理
port = 8080            # 监听端口
bind_address = 127.0.0.1  # 绑定地址
```

### HTTPS配置

```ini
[https]
enabled = true          # 启用HTTPS代理
port = 8443            # 监听端口
bind_address = 127.0.0.1  # 绑定地址
cert_path = ./certs/cert.pem  # 证书文件路径
key_path = ./certs/key.pem   # 私钥文件路径
use_lets_encrypt = false     # 使用Let's Encrypt
lets_encrypt_domain = example.com  # 域名
lets_encrypt_email = <EMAIL>  # 邮箱
```

### 认证配置

```ini
[auth]
enabled = true          # 启用认证
username = proxyuser    # 用户名
password = proxypass    # 密码
realm = Proxy Authentication Required  # 认证域
```

### 性能配置

```ini
[performance]
max_connections = 1000        # 最大连接数
connection_timeout_secs = 30  # 连接超时(秒)
keep_alive_timeout_secs = 60  # 保持连接时间(秒)
buffer_size = 8192           # 缓冲区大小(字节)
cache_size_mb = 100          # 缓存大小(MB)
worker_threads =             # 工作线程数(空为自动)
```

## 使用示例

### 配置浏览器代理

1. **Chrome/Edge**: 设置 → 高级 → 系统 → 打开代理设置
2. **Firefox**: 设置 → 网络设置 → 手动代理配置

代理设置：
- HTTP代理: `127.0.0.1:8080`
- HTTPS代理: `127.0.0.1:8443`
- 如果启用了认证，输入配置的用户名和密码

### 命令行测试

```bash
# 测试HTTP代理
curl -x http://127.0.0.1:8080 http://httpbin.org/ip

# 测试HTTPS代理
curl -x https://127.0.0.1:8443 https://httpbin.org/ip

# 带认证的代理
curl -x ***************************************** http://httpbin.org/ip
```

## TLS证书设置

### 使用自签名证书（测试用）

```bash
# 创建证书目录
mkdir certs

# 生成私钥
openssl genrsa -out certs/key.pem 2048

# 生成证书
openssl req -new -x509 -key certs/key.pem -out certs/cert.pem -days 365
```

### 使用Let's Encrypt证书

在配置文件中设置：

```ini
[https]
use_lets_encrypt = true
lets_encrypt_domain = your-domain.com
lets_encrypt_email = <EMAIL>
```

## 性能优化建议

1. **调整最大连接数**: 根据服务器性能调整 `max_connections`
2. **优化缓冲区大小**: 根据网络带宽调整 `buffer_size`
3. **设置合适的超时**: 平衡响应性和资源使用
4. **启用缓存**: 对于重复请求，缓存可以显著提升性能
5. **工作线程数**: 通常设置为CPU核心数的2倍

## 监控和日志

代理服务器会定期输出统计信息：

```
Stats - Active: 45, Total: 1250, Failed: 12, Bytes: 1024 MB
```

- **Active**: 当前活跃连接数
- **Total**: 总连接数
- **Failed**: 失败连接数
- **Bytes**: 传输的数据量

## 故障排除

### 常见问题

1. **端口被占用**: 检查端口是否被其他程序使用
2. **证书错误**: 确保证书文件路径正确且可读
3. **认证失败**: 检查用户名密码配置
4. **连接超时**: 调整超时设置或检查网络连接

### 调试模式

设置环境变量启用详细日志：

```bash
RUST_LOG=debug cargo run --release
```

## 开发和测试

### 运行测试

```bash
cargo test
```

### 性能测试

```bash
# 使用wrk进行压力测试
wrk -t12 -c400 -d30s --proxy http://127.0.0.1:8080 http://httpbin.org/get
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
