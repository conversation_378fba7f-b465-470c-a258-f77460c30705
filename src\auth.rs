use base64::{engine::general_purpose, Engine as _};
use hyper::{header, HeaderMap, StatusCode};
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct AuthManager {
    enabled: bool,
    credentials: HashMap<String, String>, // username -> password
    realm: String,
}

impl AuthManager {
    pub fn new(enabled: bool, users: Vec<(String, String)>, realm: String) -> Self {
        let mut credentials = HashMap::new();
        for (username, password) in users {
            credentials.insert(username, password);
        }

        Self {
            enabled,
            credentials,
            realm,
        }
    }

    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    pub fn authenticate(&self, headers: &HeaderMap) -> bool {
        if !self.enabled {
            return true;
        }

        if let Some(auth_header) = headers.get(header::PROXY_AUTHORIZATION) {
            if let Ok(auth_str) = auth_header.to_str() {
                if auth_str.starts_with("Basic ") {
                    let encoded = &auth_str[6..];
                    if let Ok(decoded) = general_purpose::STANDARD.decode(encoded) {
                        if let Ok(credentials) = String::from_utf8(decoded) {
                            if let Some((username, password)) = credentials.split_once(':') {
                                return self.verify_credentials(username, password);
                            }
                        }
                    }
                }
            }
        }

        false
    }

    pub fn authenticate_header(&self, auth_str: &str) -> bool {
        if !self.enabled {
            return true;
        }

        if auth_str.starts_with("Basic ") {
            let encoded = &auth_str[6..];
            if let Ok(decoded) = general_purpose::STANDARD.decode(encoded) {
                if let Ok(credentials) = String::from_utf8(decoded) {
                    if let Some((username, password)) = credentials.split_once(':') {
                        return self.verify_credentials(username, password);
                    }
                }
            }
        }

        false
    }

    pub fn get_auth_challenge(&self) -> (StatusCode, HeaderMap) {
        let mut headers = HeaderMap::new();
        let challenge = format!("Basic realm=\"{}\"", self.realm);
        headers.insert(header::PROXY_AUTHENTICATE, challenge.parse().unwrap());
        (StatusCode::PROXY_AUTHENTICATION_REQUIRED, headers)
    }

    fn verify_credentials(&self, username: &str, password: &str) -> bool {
        self.credentials.get(username)
            .map(|stored_password| stored_password == password)
            .unwrap_or(false)
    }

    pub fn add_user(&mut self, username: String, password: String) {
        self.credentials.insert(username, password);
    }

    pub fn remove_user(&mut self, username: &str) -> bool {
        self.credentials.remove(username).is_some()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use hyper::header::HeaderValue;

    #[test]
    fn test_auth_disabled() {
        let auth = AuthManager::new(false, None, None, "test".to_string());
        let headers = HeaderMap::new();
        assert!(auth.authenticate(&headers));
    }

    #[test]
    fn test_auth_enabled_no_header() {
        let auth = AuthManager::new(
            true,
            Some("user".to_string()),
            Some("pass".to_string()),
            "test".to_string(),
        );
        let headers = HeaderMap::new();
        assert!(!auth.authenticate(&headers));
    }

    #[test]
    fn test_auth_enabled_valid_credentials() {
        let auth = AuthManager::new(
            true,
            Some("user".to_string()),
            Some("pass".to_string()),
            "test".to_string(),
        );
        
        let mut headers = HeaderMap::new();
        let credentials = general_purpose::STANDARD.encode("user:pass");
        let auth_value = format!("Basic {}", credentials);
        headers.insert(header::PROXY_AUTHORIZATION, HeaderValue::from_str(&auth_value).unwrap());
        
        assert!(auth.authenticate(&headers));
    }

    #[test]
    fn test_auth_enabled_invalid_credentials() {
        let auth = AuthManager::new(
            true,
            Some("user".to_string()),
            Some("pass".to_string()),
            "test".to_string(),
        );
        
        let mut headers = HeaderMap::new();
        let credentials = general_purpose::STANDARD.encode("user:wrong");
        let auth_value = format!("Basic {}", credentials);
        headers.insert(header::PROXY_AUTHORIZATION, HeaderValue::from_str(&auth_value).unwrap());
        
        assert!(!auth.authenticate(&headers));
    }
}
