# PowerShell Service Installation Script
# Run as Administrator

param(
    [switch]$Uninstall
)

$ServiceName = "RustForwardProxy"
$DisplayName = "Rust Forward Proxy"
$Description = "High-performance forward proxy server with HTTP/HTTPS support"
$ExePath = Join-Path $PSScriptRoot "target\release\rust-forward-proxy.exe"

Write-Host "Rust Forward Proxy Service Installer" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script requires administrator privileges" -ForegroundColor Red
    Write-Host "Please run PowerShell as administrator and try again" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Administrator privileges confirmed." -ForegroundColor Green
Write-Host ""

if ($Uninstall) {
    Write-Host "Uninstalling service..." -ForegroundColor Yellow
    
    # Stop service if running
    try {
        Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
        Write-Host "Service stopped" -ForegroundColor Green
    } catch {
        Write-Host "Service was not running" -ForegroundColor Yellow
    }
    
    # Delete service
    try {
        & sc.exe delete $ServiceName
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Service uninstalled successfully" -ForegroundColor Green
        } else {
            Write-Host "✗ Service uninstallation failed or service was not installed" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ Failed to uninstall service: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Read-Host "Press Enter to exit"
    exit
}

# Build the project
Write-Host "Building project..." -ForegroundColor Yellow
try {
    & cargo build --release
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✓ Build successful" -ForegroundColor Green
} catch {
    Write-Host "✗ Build failed: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check if executable exists
if (-not (Test-Path $ExePath)) {
    Write-Host "ERROR: Executable not found at $ExePath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Service Details:" -ForegroundColor Cyan
Write-Host "  Name: $ServiceName"
Write-Host "  Display Name: $DisplayName"
Write-Host "  Executable: $ExePath"
Write-Host ""

# Stop and remove existing service if it exists
Write-Host "Cleaning up existing service..." -ForegroundColor Yellow
try {
    Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
    & sc.exe delete $ServiceName 2>$null
    Start-Sleep -Seconds 2
} catch {
    # Ignore errors - service might not exist
}

# Create the service
Write-Host "Creating Windows service..." -ForegroundColor Yellow
$BinPath = "`"$ExePath`" --service"

try {
    & sc.exe create $ServiceName binPath= $BinPath DisplayName= $DisplayName start= auto type= own
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Service created successfully" -ForegroundColor Green
        
        # Set description
        Write-Host "Setting service description..." -ForegroundColor Yellow
        & sc.exe description $ServiceName $Description
        
        # Set recovery options
        Write-Host "Setting recovery options..." -ForegroundColor Yellow
        & sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/5000/restart/5000
        
        Write-Host ""
        Write-Host "✓ Service installation completed!" -ForegroundColor Green
        Write-Host ""
        Write-Host "You can now:" -ForegroundColor Cyan
        Write-Host "  Start service: Start-Service $ServiceName"
        Write-Host "  Stop service:  Stop-Service $ServiceName"
        Write-Host "  Query status:  Get-Service $ServiceName"
        Write-Host "  Or use Services.msc to manage graphically"
        Write-Host ""
        Write-Host "The service will start automatically when Windows boots." -ForegroundColor Green
        
    } else {
        Write-Host "✗ Service creation failed" -ForegroundColor Red
        Write-Host ""
        Write-Host "Troubleshooting:" -ForegroundColor Yellow
        Write-Host "1. Ensure you're running as administrator"
        Write-Host "2. Check if the executable path is correct"
        Write-Host "3. Verify no antivirus is blocking the operation"
        Write-Host "4. Try running the command manually:"
        Write-Host "   sc create $ServiceName binPath= `"$BinPath`" DisplayName= `"$DisplayName`" start= auto type= own"
    }
    
} catch {
    Write-Host "✗ Failed to create service: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
