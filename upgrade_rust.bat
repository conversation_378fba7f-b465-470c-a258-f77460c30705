@echo off
echo Upgrading Rust to version 1.75 (last version supporting Windows 7)...

echo.
echo Current Rust version:
rustc --version

echo.
echo Checking if rustup is available...
rustup --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: rustup is not available
    echo Please install rustup from: https://rustup.rs/
    echo Or manually install Rust 1.75 from: https://forge.rust-lang.org/infra/channel-layout.html#archives
    pause
    exit /b 1
)

echo.
echo Updating to Rust 1.75...
rustup install 1.75
rustup default 1.75

echo.
echo Updated Rust version:
rustc --version

echo.
echo Verifying cargo version:
cargo --version

echo.
echo SUCCESS: Rust has been upgraded to 1.75
echo This is the last version that officially supports Windows 7
echo.
echo You can now build the HTTPS proxy with full TLS support!
echo Run: cargo build --release

pause
