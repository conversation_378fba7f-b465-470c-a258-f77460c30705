@echo off
echo Creating self-signed certificate for HTTPS proxy...

if not exist certs mkdir certs

echo.
echo Step 1: Creating private key...
openssl genrsa -out certs/key.pem 2048

echo.
echo Step 2: Creating certificate signing request...
openssl req -new -key certs/key.pem -out certs/cert.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=Test/OU=Test/CN=localhost"

echo.
echo Step 3: Creating self-signed certificate...
openssl x509 -req -days 365 -in certs/cert.csr -signkey certs/key.pem -out certs/cert.pem

echo.
echo Step 4: Creating PKCS#12 file for native-tls...
openssl pkcs12 -export -out certs/cert.p12 -inkey certs/key.pem -in certs/cert.pem -passout pass:

echo.
echo Certificate files created:
echo   certs/key.pem  - Private key
echo   certs/cert.pem - Certificate
echo   certs/cert.p12 - PKCS#12 file (for HTTPS proxy)

echo.
echo Update your proxy.ini file to use these certificates:
echo   cert_path = ./certs/cert.pem
echo   key_path = ./certs/key.pem

echo.
echo Note: This creates a self-signed certificate for testing only.
echo For production use, obtain certificates from a trusted CA.

pause
