use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::Path;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyConfig {
    pub http: HttpConfig,
    pub https: HttpsConfig,
    pub auth: AuthConfig,
    pub performance: PerformanceConfig,
    pub dns: DnsConfig,
    pub logging: LoggingConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HttpConfig {
    pub enabled: bool,
    pub port: u16,
    pub bind_address: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HttpsConfig {
    pub enabled: bool,
    pub port: u16,
    pub bind_address: String,
    pub cert_path: String,
    pub key_path: String,
    pub use_pem_files: bool,
    // Legacy PFX support (not used with rustls)
    pub pfx_path: Option<String>,
    pub pfx_password: Option<String>,
    pub use_lets_encrypt: bool,
    pub lets_encrypt_domain: Option<String>,
    pub lets_encrypt_email: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthConfig {
    pub enabled: bool,
    pub users: Vec<(String, String)>, // (username, password) pairs
    pub realm: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub max_connections: usize,
    pub connection_timeout_secs: u64,
    pub keep_alive_timeout_secs: u64,
    pub buffer_size: usize,
    pub cache_size_mb: usize,
    pub worker_threads: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DnsConfig {
    pub servers: Vec<String>,
    pub timeout: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub file_path: Option<String>,
}

impl Default for ProxyConfig {
    fn default() -> Self {
        Self {
            http: HttpConfig {
                enabled: true,
                port: 8080,
                bind_address: "127.0.0.1".to_string(),
            },
            https: HttpsConfig {
                enabled: false,
                port: 8443,
                bind_address: "127.0.0.1".to_string(),
                cert_path: "./ca/d9.fit/fullchain.pem".to_string(),
                key_path: "./ca/d9.fit/privkey.pem".to_string(),
                use_pem_files: true,
                pfx_path: None,
                pfx_password: None,
                use_lets_encrypt: false,
                lets_encrypt_domain: None,
                lets_encrypt_email: None,
            },
            auth: AuthConfig {
                enabled: false,
                users: vec![],
                realm: "Proxy Authentication Required".to_string(),
            },
            performance: PerformanceConfig {
                max_connections: 1000,
                connection_timeout_secs: 30,
                keep_alive_timeout_secs: 60,
                buffer_size: 8192,
                cache_size_mb: 100,
                worker_threads: None,
            },
            dns: DnsConfig {
                servers: vec!["*******".to_string(), "*******".to_string()],
                timeout: 5,
            },
            logging: LoggingConfig {
                level: "info".to_string(),
                file_path: None,
            },
        }
    }
}

impl ProxyConfig {
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        // For now, use a simple file-based configuration
        // In a full implementation, you would use a proper INI parser
        let content = fs::read_to_string(path.as_ref())?;
        let config_map = Self::parse_simple_ini(&content)?;

        let mut config = Self::default();

        // Parse HTTP section
        if let Some(section) = config_map.get("http") {
            if let Some(enabled) = section.get("enabled") {
                config.http.enabled = enabled.parse().unwrap_or(true);
            }
            if let Some(port) = section.get("port") {
                config.http.port = port.parse().unwrap_or(8080);
            }
            if let Some(bind_address) = section.get("bind_address") {
                config.http.bind_address = bind_address.clone();
            }
        }

        // Parse HTTPS section
        if let Some(section) = config_map.get("https") {
            if let Some(enabled) = section.get("enabled") {
                config.https.enabled = enabled.parse().unwrap_or(false);
            }
            if let Some(port) = section.get("port") {
                config.https.port = port.parse().unwrap_or(8443);
            }
            if let Some(bind_address) = section.get("bind_address") {
                config.https.bind_address = bind_address.clone();
            }
            if let Some(cert_path) = section.get("cert_path") {
                config.https.cert_path = cert_path.clone();
            }
            if let Some(key_path) = section.get("key_path") {
                config.https.key_path = key_path.clone();
            }
            if let Some(use_pem) = section.get("use_pem_files") {
                config.https.use_pem_files = use_pem.parse().unwrap_or(true);
            }
            if let Some(pfx_path) = section.get("pfx_path") {
                config.https.pfx_path = Some(pfx_path.clone());
            }
            if let Some(pfx_password) = section.get("pfx_password") {
                config.https.pfx_password = Some(pfx_password.clone());
            }
            if let Some(use_lets_encrypt) = section.get("use_lets_encrypt") {
                config.https.use_lets_encrypt = use_lets_encrypt.parse().unwrap_or(false);
            }
            if let Some(domain) = section.get("lets_encrypt_domain") {
                config.https.lets_encrypt_domain = Some(domain.clone());
            }
            if let Some(email) = section.get("lets_encrypt_email") {
                config.https.lets_encrypt_email = Some(email.clone());
            }
        }

        // Parse Auth section
        if let Some(section) = config_map.get("auth") {
            if let Some(enabled) = section.get("enabled") {
                config.auth.enabled = enabled.parse().unwrap_or(false);
            }
            if let Some(users_str) = section.get("users") {
                config.auth.users = Self::parse_users(users_str);
            }
            if let Some(realm) = section.get("realm") {
                config.auth.realm = realm.clone();
            }
        }

        // Parse Performance section
        if let Some(section) = config_map.get("performance") {
            if let Some(max_connections) = section.get("max_connections") {
                config.performance.max_connections = max_connections.parse().unwrap_or(1000);
            }
            if let Some(timeout) = section.get("connection_timeout_secs") {
                config.performance.connection_timeout_secs = timeout.parse().unwrap_or(30);
            }
            if let Some(keep_alive) = section.get("keep_alive_timeout_secs") {
                config.performance.keep_alive_timeout_secs = keep_alive.parse().unwrap_or(60);
            }
            if let Some(buffer_size) = section.get("buffer_size") {
                config.performance.buffer_size = buffer_size.parse().unwrap_or(8192);
            }
            if let Some(cache_size) = section.get("cache_size_mb") {
                config.performance.cache_size_mb = cache_size.parse().unwrap_or(100);
            }
            if let Some(worker_threads) = section.get("worker_threads") {
                config.performance.worker_threads = worker_threads.parse().ok();
            }
        }

        // Parse DNS section
        if let Some(section) = config_map.get("dns") {
            if let Some(servers_str) = section.get("servers") {
                config.dns.servers = servers_str.split(',')
                    .map(|s| s.trim().to_string())
                    .filter(|s| !s.is_empty())
                    .collect();
            }
            if let Some(timeout) = section.get("timeout") {
                config.dns.timeout = timeout.parse().unwrap_or(5);
            }
        }

        // Parse Logging section
        if let Some(section) = config_map.get("logging") {
            if let Some(level) = section.get("level") {
                config.logging.level = level.clone();
            }
            if let Some(file_path) = section.get("file_path") {
                config.logging.file_path = Some(file_path.clone());
            }
        }

        Ok(config)
    }

    pub fn validate(&self) -> Result<()> {
        if !self.http.enabled && !self.https.enabled {
            anyhow::bail!("At least one of HTTP or HTTPS must be enabled");
        }

        if self.https.enabled && !self.https.use_lets_encrypt {
            if !Path::new(&self.https.cert_path).exists() {
                anyhow::bail!("HTTPS cert file not found: {}", self.https.cert_path);
            }
            if !Path::new(&self.https.key_path).exists() {
                anyhow::bail!("HTTPS key file not found: {}", self.https.key_path);
            }
        }

        if self.auth.enabled {
            if self.auth.users.is_empty() {
                anyhow::bail!("At least one user must be configured when auth is enabled");
            }
        }

        Ok(())
    }

    fn parse_simple_ini(content: &str) -> Result<HashMap<String, HashMap<String, String>>> {
        let mut sections = HashMap::new();
        let mut current_section = String::new();
        let mut current_map = HashMap::new();

        for line in content.lines() {
            let line = line.trim();
            if line.is_empty() || line.starts_with('#') || line.starts_with(';') {
                continue;
            }

            if line.starts_with('[') && line.ends_with(']') {
                // Save previous section
                if !current_section.is_empty() {
                    sections.insert(current_section.clone(), current_map.clone());
                }
                // Start new section
                current_section = line[1..line.len()-1].to_string();
                current_map.clear();
            } else if let Some((key, value)) = line.split_once('=') {
                current_map.insert(key.trim().to_string(), value.trim().to_string());
            }
        }

        // Save last section
        if !current_section.is_empty() {
            sections.insert(current_section, current_map);
        }

        Ok(sections)
    }

    fn parse_users(users_str: &str) -> Vec<(String, String)> {
        users_str.split(',')
            .filter_map(|user_pass| {
                if let Some((user, pass)) = user_pass.split_once(':') {
                    Some((user.trim().to_string(), pass.trim().to_string()))
                } else {
                    None
                }
            })
            .collect()
    }
}
