@echo off
echo Simple Service Installation Script
echo ==================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ERROR: Administrator privileges required
    echo Please run this script as administrator
    pause
    exit /b 1
)

echo Administrator privileges confirmed.
echo.

REM 构建项目
echo Building project...
cargo build --release
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful.
echo.

REM 设置变量
set SERVICE_NAME=RustForwardProxy
set DISPLAY_NAME=Rust Forward Proxy
set DESCRIPTION=High-performance forward proxy server with HTTP/HTTPS support
set EXE_PATH=%CD%\target\release\rust-forward-proxy.exe

echo Service Name: %SERVICE_NAME%
echo Display Name: %DISPLAY_NAME%
echo Executable: %EXE_PATH%
echo.

REM 检查可执行文件
if not exist "%EXE_PATH%" (
    echo ERROR: Executable not found at %EXE_PATH%
    pause
    exit /b 1
)

REM 停止并删除现有服务（如果存在）
echo Cleaning up existing service...
sc stop %SERVICE_NAME% >nul 2>&1
sc delete %SERVICE_NAME% >nul 2>&1
timeout /t 2 /nobreak >nul

REM 创建服务
echo.
echo Creating Windows service...
sc create %SERVICE_NAME% binPath= "\"%EXE_PATH%\" --service" DisplayName= "%DISPLAY_NAME%" start= auto type= own

if %ERRORLEVEL% EQU 0 (
    echo ✓ Service created successfully
    
    REM 设置描述
    echo Setting service description...
    sc description %SERVICE_NAME% "%DESCRIPTION%"
    
    REM 设置恢复选项
    echo Setting recovery options...
    sc failure %SERVICE_NAME% reset= 86400 actions= restart/5000/restart/5000/restart/5000
    
    echo.
    echo ✓ Service installation completed!
    echo.
    echo You can now:
    echo   Start service: sc start %SERVICE_NAME%
    echo   Stop service:  sc stop %SERVICE_NAME%
    echo   Query status:  sc query %SERVICE_NAME%
    echo   Delete service: sc delete %SERVICE_NAME%
    echo.
    echo Or use Services.msc to manage the service graphically.
    echo The service will start automatically when Windows boots.
    
) else (
    echo ✗ Service creation failed
    echo.
    echo Troubleshooting:
    echo 1. Make sure you're running as administrator
    echo 2. Check if the executable path is correct
    echo 3. Ensure no antivirus is blocking the operation
)

echo.
pause
