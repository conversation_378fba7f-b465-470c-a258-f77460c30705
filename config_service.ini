# Rust Forward Proxy Configuration for Windows Service
# This configuration uses absolute paths to avoid working directory issues

[http]
enabled = true
port = 2000
bind_address = 0.0.0.0

[https]
enabled = true
port = 2443
bind_address = 0.0.0.0

# Certificate configuration with absolute paths
# Replace C:\Service\https代理服务器rust with your actual path
use_pem_files = true
cert_path = C:\Service\https代理服务器rust\ca\d9.fit\fullchain.pem
key_path = C:\Service\https代理服务器rust\ca\d9.fit\privkey.pem

# Let's Encrypt configuration (future feature)
use_lets_encrypt = false

[auth]
enabled = true
# Format: username:password,username2:password2
users = zdw:z7758521
realm = Proxy Server

[dns]
# Custom DNS servers (comma-separated)
servers = *******,*******
timeout = 5

[performance]
max_connections = 1000
connection_timeout_secs = 30
keep_alive_timeout_secs = 60
buffer_size = 8192
cache_size_mb = 100

[logging]
level = info
# Possible values: error, warn, info, debug, trace
