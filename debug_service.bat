@echo off
echo Debugging Service Installation
echo ==============================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ERROR: This script requires administrator privileges
    echo Please run as administrator
    pause
    exit /b 1
)

echo Administrator privileges confirmed.
echo.

REM 获取当前目录和可执行文件路径
set CURRENT_DIR=%CD%
set EXE_PATH=%CURRENT_DIR%\target\release\rust-forward-proxy.exe

echo Current directory: %CURRENT_DIR%
echo Executable path: %EXE_PATH%
echo.

REM 检查可执行文件是否存在
if not exist "%EXE_PATH%" (
    echo ERROR: Executable not found at %EXE_PATH%
    echo Please build the project first: cargo build --release
    pause
    exit /b 1
)

echo Executable found.
echo.

REM 手动测试sc create命令
echo Testing sc create command manually...
echo.

REM 先删除可能存在的服务
echo Cleaning up any existing service...
sc stop RustForwardProxy >nul 2>&1
sc delete RustForwardProxy >nul 2>&1
timeout /t 2 /nobreak >nul

echo.
echo Creating service with sc command:
echo sc create RustForwardProxy binPath="%EXE_PATH% --service" DisplayName="Rust Forward Proxy" start=auto type=own

sc create RustForwardProxy binPath="%EXE_PATH% --service" DisplayName="Rust Forward Proxy" start=auto type=own

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Service created successfully with manual command!
    echo.
    echo Setting description...
    sc description RustForwardProxy "High-performance forward proxy server with HTTP/HTTPS support"
    
    echo.
    echo Service created. You can now:
    echo   sc start RustForwardProxy
    echo   sc query RustForwardProxy
    echo   sc stop RustForwardProxy
    echo   sc delete RustForwardProxy
) else (
    echo.
    echo ✗ Manual service creation failed
    echo This helps us understand the issue
)

echo.
echo Now testing the program's install command...
echo.

"%EXE_PATH%" --install-service

echo.
echo Debug completed.
pause
