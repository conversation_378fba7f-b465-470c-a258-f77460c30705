# Rust Forward Proxy Configuration File
# 
# This file configures the behavior of the forward proxy server.
# All settings have reasonable defaults if not specified.

[http]
# Enable HTTP proxy server
enabled = true
# HTTP proxy listening port
port = 8080
# Bind address (use 0.0.0.0 to listen on all interfaces)
bind_address = 0.0.0.0

[https]
# Enable HTTPS proxy server
enabled = true
# HTTPS proxy listening port
port = 8443
# Bind address (use 0.0.0.0 to listen on all interfaces)
bind_address = 0.0.0.0
# Path to TLS certificate file (PEM format) - use fullchain.pem for Let's Encrypt
cert_path = ./ca/d9.fit/fullchain.pem
# Path to TLS private key file (PEM format)
key_path = ./ca/d9.fit/privkey.pem
# Use existing PFX file
use_pem_files = false
pfx_path = ./ca/d9.fit/d9.pfx
pfx_password =
# Use Let's Encrypt for automatic certificate management
use_lets_encrypt = false
# Domain name for Let's Encrypt certificate (required if use_lets_encrypt = true)
lets_encrypt_domain = d9.fit
# Email address for Let's Encrypt registration (required if use_lets_encrypt = true)
lets_encrypt_email = <EMAIL>

[auth]
# Enable proxy authentication
enabled = true
# Multiple users (format: username:password,username:password)
users = zdw:z7758521,user1z:pass1z,user2z:pass2z
# Authentication realm message
realm = Proxy Authentication Required

[performance]
# Maximum number of concurrent connections
max_connections = 1000
# Connection timeout in seconds
connection_timeout_secs = 30
# Keep-alive timeout in seconds
keep_alive_timeout_secs = 60
# Buffer size for data transfer (bytes)
buffer_size = 8192
# Cache size in megabytes
cache_size_mb = 100
# Number of worker threads (leave empty for auto-detection)
worker_threads = 

[dns]
# DNS servers (comma separated, leave empty for system default)
servers = *******,*******
# DNS timeout in seconds
timeout = 5

[logging]
# Log level: error, warn, info, debug, trace
level = info
# Log file path (leave empty to log to console only)
file_path =
