use crate::config::HttpsConfig;
use anyhow::{Context, Result};
use log::{info, warn};
use rustls::{Certificate, PrivateKey, ServerConfig};
use rustls_pemfile::{certs, pkcs8_private_keys};
use std::fs::File;
use std::io::BufReader;
use std::path::Path;
use std::sync::Arc;
use tokio_rustls::TlsAcceptor;


pub struct TlsManager {
    config: HttpsConfig,
}

impl TlsManager {
    pub fn new(config: HttpsConfig) -> Self {
        Self { config }
    }

    pub async fn create_acceptor(&self) -> Result<TlsAcceptor> {
        let server_config = if self.config.use_lets_encrypt {
            self.create_lets_encrypt_config().await?
        } else {
            self.create_pem_based_config()?
        };

        Ok(TlsAcceptor::from(Arc::new(server_config)))
    }

    fn create_pem_based_config(&self) -> Result<ServerConfig> {
        info!("Loading TLS certificates from PEM files");

        // Load certificates
        let cert_file = File::open(&self.config.cert_path)
            .with_context(|| format!("Failed to open cert file: {}", self.config.cert_path))?;
        let mut cert_reader = BufReader::new(cert_file);
        let cert_chain = certs(&mut cert_reader)
            .with_context(|| "Failed to parse certificate chain")?
            .into_iter()
            .map(Certificate)
            .collect();

        // Load private key
        let key_file = File::open(&self.config.key_path)
            .with_context(|| format!("Failed to open key file: {}", self.config.key_path))?;
        let mut key_reader = BufReader::new(key_file);
        let mut keys = pkcs8_private_keys(&mut key_reader)
            .with_context(|| "Failed to parse private key")?;

        if keys.is_empty() {
            anyhow::bail!("No private keys found in key file");
        }

        let private_key = PrivateKey(keys.remove(0));

        // Create server config with Windows 7 compatibility
        let config = ServerConfig::builder()
            .with_safe_default_cipher_suites()
            .with_safe_default_kx_groups()
            .with_safe_default_protocol_versions()
            .with_context(|| "Failed to create TLS config builder")?
            .with_no_client_auth()
            .with_single_cert(cert_chain, private_key)
            .with_context(|| "Failed to create TLS server config")?;

        info!("TLS configuration loaded successfully from PEM files");
        Ok(config)
    }



    async fn create_lets_encrypt_config(&self) -> Result<ServerConfig> {
        // This is a simplified implementation
        // In a real-world scenario, you would integrate with ACME client libraries
        // like `acme2` or `instant-acme` for automatic certificate management
        
        warn!("Let's Encrypt integration is not fully implemented in this example");
        warn!("Falling back to file-based certificates");
        
        // For now, fall back to file-based config
        // In production, you would:
        // 1. Use an ACME client to request certificates
        // 2. Handle certificate renewal
        // 3. Store certificates securely
        // 4. Implement proper error handling and retry logic
        
        if let (Some(domain), Some(email)) = (&self.config.lets_encrypt_domain, &self.config.lets_encrypt_email) {
            info!("Would request Let's Encrypt certificate for domain: {}, email: {}", domain, email);
            // TODO: Implement ACME client integration
        }
        
        // Fall back to PEM files
        self.create_pem_based_config()
    }



    pub fn validate_certificates(&self) -> Result<()> {
        if self.config.use_lets_encrypt {
            if self.config.lets_encrypt_domain.is_none() {
                anyhow::bail!("Let's Encrypt domain must be specified when use_lets_encrypt is true");
            }
            if self.config.lets_encrypt_email.is_none() {
                anyhow::bail!("Let's Encrypt email must be specified when use_lets_encrypt is true");
            }
        } else if self.config.use_pem_files {
            // Check PEM files
            if !Path::new(&self.config.cert_path).exists() {
                anyhow::bail!("Certificate file not found: {}", self.config.cert_path);
            }
            if !Path::new(&self.config.key_path).exists() {
                anyhow::bail!("Private key file not found: {}", self.config.key_path);
            }
            info!("Using PEM certificate files for HTTPS");
        } else {
            // Check for PFX file
            if let Some(pfx_path) = &self.config.pfx_path {
                if !Path::new(pfx_path).exists() {
                    anyhow::bail!("PFX file not found: {}", pfx_path);
                }
                return Ok(());
            }
            anyhow::bail!("No certificate configuration found. Please set use_pem_files=true or provide pfx_path");
        }
        Ok(())
    }
}

// Helper function to create self-signed certificates for testing
#[cfg(feature = "self-signed")]
pub fn create_self_signed_cert(domain: &str) -> Result<(Vec<u8>, Vec<u8>)> {
    use rcgen::{Certificate, CertificateParams, DistinguishedName};
    use time::{Duration, OffsetDateTime};

    let mut params = CertificateParams::new(vec![domain.to_string()]);
    params.not_before = OffsetDateTime::now_utc();
    params.not_after = OffsetDateTime::now_utc() + Duration::days(365);
    
    let mut distinguished_name = DistinguishedName::new();
    distinguished_name.push(rcgen::DnType::CommonName, domain);
    params.distinguished_name = distinguished_name;

    let cert = Certificate::from_params(params)?;
    let cert_pem = cert.serialize_pem()?;
    let key_pem = cert.serialize_private_key_pem();

    Ok((cert_pem.into_bytes(), key_pem.into_bytes()))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_tls_manager_creation() {
        let config = HttpsConfig {
            enabled: true,
            port: 8443,
            bind_address: "127.0.0.1".to_string(),
            cert_path: "./test_cert.pem".to_string(),
            key_path: "./test_key.pem".to_string(),
            use_lets_encrypt: false,
            lets_encrypt_domain: None,
            lets_encrypt_email: None,
        };

        let tls_manager = TlsManager::new(config);
        assert!(!tls_manager.config.use_lets_encrypt);
    }

    #[test]
    fn test_validate_certificates_file_based() {
        let config = HttpsConfig {
            enabled: true,
            port: 8443,
            bind_address: "127.0.0.1".to_string(),
            cert_path: "./nonexistent_cert.pem".to_string(),
            key_path: "./nonexistent_key.pem".to_string(),
            use_lets_encrypt: false,
            lets_encrypt_domain: None,
            lets_encrypt_email: None,
        };

        let tls_manager = TlsManager::new(config);
        assert!(tls_manager.validate_certificates().is_err());
    }

    #[test]
    fn test_validate_certificates_lets_encrypt() {
        let config = HttpsConfig {
            enabled: true,
            port: 8443,
            bind_address: "127.0.0.1".to_string(),
            cert_path: "".to_string(),
            key_path: "".to_string(),
            use_lets_encrypt: true,
            lets_encrypt_domain: None,
            lets_encrypt_email: None,
        };

        let tls_manager = TlsManager::new(config);
        assert!(tls_manager.validate_certificates().is_err());
    }
}
