# Rust Forward Proxy Configuration
# This is the default configuration file

[http]
enabled = true
port = 8080
bind_address = 0.0.0.0

[https]
enabled = true
port = 8443
bind_address = 0.0.0.0

# Certificate configuration (choose one method)
# Method 1: Use PEM files (recommended for Let's Encrypt)
use_pem_files = true
cert_path = ./ca/d9.fit/fullchain.pem
key_path = ./ca/d9.fit/privkey.pem

# Method 2: Use PFX/PKCS#12 file (legacy, not recommended)
# use_pem_files = false
# pfx_path = ./ca/d9.fit/certificate.pfx
# pfx_password = your_password_here

# Let's Encrypt configuration (future feature)
use_lets_encrypt = false
# lets_encrypt_domain = your-domain.com
# lets_encrypt_email = <EMAIL>

[auth]
enabled = true
# Format: username:password,username2:password2
users = zdw:z7758521
realm = Proxy Server

[dns]
# Custom DNS servers (comma-separated)
servers = *******,*******
timeout = 5

[performance]
max_connections = 1000
connection_timeout_secs = 30
keep_alive_timeout_secs = 60
buffer_size = 8192
cache_size_mb = 100

[logging]
level = info
# Possible values: error, warn, info, debug, trace
