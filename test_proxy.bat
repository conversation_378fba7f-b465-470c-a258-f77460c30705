@echo off
echo Testing Rust Forward Proxy...

echo.
echo Building the proxy...
cargo build --release

if %ERRORLEVEL% NEQ 0 (
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Starting proxy server in background...
start /B target\release\rust-forward-proxy.exe proxy.ini

echo Waiting for server to start...
timeout /t 3 /nobreak >nul

echo.
echo Testing HTTP proxy...
curl -x http://127.0.0.1:8080 http://httpbin.org/ip --connect-timeout 10

echo.
echo Testing HTTPS proxy...
curl -x http://127.0.0.1:8080 https://httpbin.org/ip --connect-timeout 10

echo.
echo Proxy test completed!
echo.
echo To stop the proxy, press Ctrl+C in the proxy window or use Task Manager
echo.
echo Configuration:
echo   HTTP Proxy:  127.0.0.1:8080
echo   HTTPS Proxy: 127.0.0.1:8443
echo   Domain: d9.fit

pause
