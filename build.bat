@echo off
echo Building Rust Forward Proxy...

echo.
echo Checking Rust version...
rustc --version
cargo --version

echo.
echo Building project...
cargo build --release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful!
    echo Executable location: target\release\rust-forward-proxy.exe
    echo.
    echo To run the proxy:
    echo   target\release\rust-forward-proxy.exe [config-file]
    echo.
    echo Example:
    echo   target\release\rust-forward-proxy.exe proxy.ini
) else (
    echo.
    echo Build failed! Please check the error messages above.
    exit /b 1
)

pause
