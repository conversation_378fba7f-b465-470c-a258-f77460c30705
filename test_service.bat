@echo off
echo Testing Rust Forward Proxy Service Features
echo ===========================================
echo.

echo 1. Testing help command...
target\release\rust-forward-proxy.exe --help
echo.

echo 2. Testing console mode (will start for 5 seconds)...
echo Starting proxy in console mode...
start /B target\release\rust-forward-proxy.exe config.ini
timeout /t 5 /nobreak >nul
taskkill /F /IM rust-forward-proxy.exe >nul 2>&1
echo Proxy stopped.
echo.

echo 3. Service installation test (requires admin privileges)...
echo To test service installation, run as administrator:
echo   target\release\rust-forward-proxy.exe --install-service
echo.

echo 4. Available service management scripts:
echo   install_service.bat   - Install as Windows service (requires admin)
echo   uninstall_service.bat - Uninstall Windows service (requires admin)
echo   manage_service.bat    - Full service management menu (requires admin)
echo.

echo Test completed!
pause
