@echo off
echo Converting PEM certificates to PKCS#12 format for HTTPS proxy...

echo.
echo Checking if OpenSSL is available...
openssl version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenSSL is not installed or not in PATH
    echo Please install OpenSSL from: https://slproweb.com/products/Win32OpenSSL.html
    echo Or use Git Bash which includes OpenSSL
    pause
    exit /b 1
)

echo.
echo Converting ca\d9.fit certificates to PKCS#12 format...

if not exist "ca\d9.fit\fullchain.pem" (
    echo ERROR: ca\d9.fit\fullchain.pem not found
    pause
    exit /b 1
)

if not exist "ca\d9.fit\privkey.pem" (
    echo ERROR: ca\d9.fit\privkey.pem not found
    pause
    exit /b 1
)

echo.
echo Creating PKCS#12 file without password...
openssl pkcs12 -export -out "ca\d9.fit\d9.p12" -inkey "ca\d9.fit\privkey.pem" -in "ca\d9.fit\fullchain.pem" -passout pass:

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: Created ca\d9.fit\d9.p12
    echo.
    echo Your proxy.ini is already configured to use this certificate:
    echo   pfx_path = ./ca/d9.fit/d9.p12
    echo   pfx_password = 
    echo.
    echo You can now run the HTTPS proxy server!
) else (
    echo.
    echo ERROR: Failed to create PKCS#12 file
    echo Please check that the PEM files are valid
)

echo.
pause
