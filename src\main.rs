mod auth;
mod config;
mod performance;
mod proxy;
mod service;
mod tls;

use anyhow::Result;
use config::ProxyConfig;
use log::{error, info};
use performance::PerformanceManager;
use proxy::ProxyServer;
use std::env;
use std::sync::Arc;
use tokio::signal;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();

    info!("Starting Rust Forward Proxy Server");

    // Check for command line arguments
    let args: Vec<String> = env::args().collect();

    // Handle service-related commands
    if args.len() > 1 {
        match args[1].as_str() {
            "--help" | "-h" => {
                print_help(&args[0]);
                return Ok(());
            }
            "--service" => {
                #[cfg(windows)]
                {
                    info!("Starting as Windows service");
                    if let Err(e) = service::run_service() {
                        error!("Service failed: {}", e);
                        return Err(anyhow::anyhow!("Service error: {}", e));
                    }
                    return Ok(());
                }
                #[cfg(not(windows))]
                {
                    eprintln!("Windows service mode is only supported on Windows");
                    return Err(anyhow::anyhow!("Service mode not supported on this platform"));
                }
            }
            "--install-service" => {
                #[cfg(windows)]
                {
                    install_service()?;
                    return Ok(());
                }
                #[cfg(not(windows))]
                {
                    eprintln!("Service installation is only supported on Windows");
                    return Err(anyhow::anyhow!("Service installation not supported"));
                }
            }
            "--uninstall-service" => {
                #[cfg(windows)]
                {
                    uninstall_service()?;
                    return Ok(());
                }
                #[cfg(not(windows))]
                {
                    eprintln!("Service uninstallation is only supported on Windows");
                    return Err(anyhow::anyhow!("Service uninstallation not supported"));
                }
            }
            _ => {
                // Continue with normal execution using the argument as config file
            }
        }
    }

    // Load configuration
    let config_path = env::args()
        .nth(1)
        .unwrap_or_else(|| "config.ini".to_string());

    let config = if std::path::Path::new(&config_path).exists() {
        info!("Loading configuration from: {}", config_path);
        match ProxyConfig::from_file(&config_path) {
            Ok(cfg) => {
                info!("Configuration loaded successfully");
                cfg
            },
            Err(e) => {
                error!("Failed to load config from {}: {}", config_path, e);
                return Err(e);
            }
        }
    } else {
        info!("Config file {} not found, using default configuration", config_path);
        ProxyConfig::default()
    };

    // Validate configuration
    info!("Validating configuration...");
    match config.validate() {
        Ok(()) => info!("Configuration validation passed"),
        Err(e) => {
            error!("Configuration validation failed: {}", e);
            return Err(e);
        }
    }
    info!("HTTP enabled: {}, port: {}", config.http.enabled, config.http.port);
    info!("HTTPS enabled: {}, port: {}", config.https.enabled, config.https.port);
    info!("Authentication enabled: {}", config.auth.enabled);

    // Configure tokio runtime based on performance settings
    if let Some(worker_threads) = config.performance.worker_threads {
        info!("Using {} worker threads", worker_threads);
    }

    // Create performance manager
    let performance_manager = Arc::new(PerformanceManager::new(config.performance.clone()));
    
    // Note: Cache cleanup will be done periodically in a simpler way

    // Create proxy server
    let proxy_server = ProxyServer::new(config.clone());

    // Start servers
    let mut tasks = Vec::new();

    // Start HTTP server if enabled
    if proxy_server.config.http.enabled {
        let proxy_clone = proxy_server.clone();
        tasks.push(tokio::spawn(async move {
            if let Err(e) = proxy_clone.start_http_server().await {
                error!("HTTP server error: {}", e);
            }
        }));
    }

    // Start HTTPS server if enabled
    if proxy_server.config.https.enabled {
        let proxy_clone = proxy_server.clone();
        tasks.push(tokio::spawn(async move {
            if let Err(e) = proxy_clone.start_https_server().await {
                error!("HTTPS server error: {}", e);
            }
        }));
    }

    // Start statistics reporting task
    let perf_manager_clone = performance_manager.clone();
    tasks.push(tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));
        loop {
            interval.tick().await;
            let stats = perf_manager_clone.get_stats();
            info!(
                "Stats - Active: {}, Total: {}, Failed: {}, Bytes: {} MB",
                stats.active_connections,
                stats.total_connections,
                stats.failed_connections,
                stats.bytes_transferred / (1024 * 1024)
            );
        }
    }));

    if tasks.is_empty() {
        error!("No servers enabled. Please enable at least HTTP or HTTPS in the configuration.");
        return Ok(());
    }

    info!("All servers started successfully");

    // Wait for shutdown signal
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("Received Ctrl+C, shutting down...");
        }
        _ = futures::future::join_all(tasks) => {
            info!("All servers stopped");
        }
    }

    info!("Proxy server shutdown complete");
    Ok(())
}

fn print_help(program_name: &str) {
    println!("Rust Forward Proxy Server");
    println!("Usage: {} [OPTIONS] [config_file]", program_name);
    println!("Default config file: config.ini");
    println!();
    println!("OPTIONS:");
    println!("  -h, --help              Show this help message");
    println!("  --service               Run as Windows service (internal use)");
    println!("  --install-service       Install as Windows service");
    println!("  --uninstall-service     Uninstall Windows service");
    println!();
    println!("Examples:");
    println!("  {}                      # Run in console mode with config.ini", program_name);
    println!("  {} proxy.ini            # Run in console mode with proxy.ini", program_name);
    println!("  {} --install-service    # Install as Windows service", program_name);
    println!("  {} --uninstall-service  # Uninstall Windows service", program_name);
    println!();
    println!("Configuration:");
    println!("  Edit config.ini to customize proxy settings");
    println!("  Supports HTTP/HTTPS proxy with authentication");
    println!();
    println!("Service Mode:");
    println!("  When installed as a service, the proxy will:");
    println!("  - Start automatically with Windows");
    println!("  - Run in the background");
    println!("  - Look for config files in standard locations");
}

#[cfg(windows)]
fn install_service() -> anyhow::Result<()> {
    use std::process::Command;

    // 检查管理员权限
    if !is_admin() {
        eprintln!("ERROR: Administrator privileges required to install service");
        eprintln!("Please run this command as administrator:");
        eprintln!("  1. Right-click Command Prompt");
        eprintln!("  2. Select 'Run as administrator'");
        eprintln!("  3. Navigate to this directory");
        eprintln!("  4. Run the command again");
        return Err(anyhow::anyhow!("Administrator privileges required"));
    }

    let exe_path = std::env::current_exe()?;
    let exe_path_str = exe_path.to_string_lossy();

    println!("Installing {} as Windows service...", service::SERVICE_DISPLAY_NAME);
    println!("Executable path: {}", exe_path_str);

    // 先检查服务是否已存在
    let check_output = Command::new("sc")
        .args(&["query", service::SERVICE_NAME])
        .output()?;

    if check_output.status.success() {
        println!("Service already exists. Stopping and deleting first...");

        // 停止现有服务
        let _ = Command::new("sc")
            .args(&["stop", service::SERVICE_NAME])
            .output();

        // 等待服务停止
        std::thread::sleep(std::time::Duration::from_secs(2));

        // 删除现有服务
        let delete_output = Command::new("sc")
            .args(&["delete", service::SERVICE_NAME])
            .output()?;

        if !delete_output.status.success() {
            let error = String::from_utf8_lossy(&delete_output.stderr);
            eprintln!("Warning: Failed to delete existing service: {}", error);
        }

        // 等待删除完成
        std::thread::sleep(std::time::Duration::from_secs(2));
    }

    // 创建新服务
    let output = Command::new("sc")
        .args(&[
            "create",
            service::SERVICE_NAME,
            &format!("binPath= \"{}\" --service", exe_path_str),
            &format!("DisplayName= {}", service::SERVICE_DISPLAY_NAME),
            "start= auto",
            "type= own",
        ])
        .output()?;

    if output.status.success() {
        println!("✓ Service created successfully");

        // 设置服务描述
        let desc_output = Command::new("sc")
            .args(&[
                "description",
                service::SERVICE_NAME,
                service::SERVICE_DESCRIPTION,
            ])
            .output()?;

        if desc_output.status.success() {
            println!("✓ Service description set");
        }

        // 设置服务恢复选项（失败时自动重启）
        let _ = Command::new("sc")
            .args(&[
                "failure",
                service::SERVICE_NAME,
                "reset= 86400",
                "actions= restart/5000/restart/5000/restart/5000",
            ])
            .output();

        println!("✓ Service recovery options configured");
        println!();
        println!("Service installed successfully!");
        println!();
        println!("You can now:");
        println!("  - Start the service: sc start {}", service::SERVICE_NAME);
        println!("  - Stop the service:  sc stop {}", service::SERVICE_NAME);
        println!("  - Check status:      sc query {}", service::SERVICE_NAME);
        println!("  - Or use Services.msc to manage the service");
        println!();
        println!("The service will start automatically when Windows boots.");

    } else {
        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);
        eprintln!("Failed to create service:");
        eprintln!("STDOUT: {}", stdout);
        eprintln!("STDERR: {}", stderr);
        return Err(anyhow::anyhow!("Service creation failed"));
    }

    Ok(())
}

#[cfg(windows)]
fn is_admin() -> bool {
    use std::process::Command;

    // 尝试执行需要管理员权限的命令
    let output = Command::new("net")
        .args(&["session"])
        .output();

    match output {
        Ok(output) => output.status.success(),
        Err(_) => false,
    }
}

#[cfg(windows)]
fn uninstall_service() -> anyhow::Result<()> {
    use std::process::Command;

    println!("Uninstalling {} Windows service...", service::SERVICE_DISPLAY_NAME);

    // 先尝试停止服务
    let _ = Command::new("sc")
        .args(&["stop", service::SERVICE_NAME])
        .output();

    // 删除服务
    let output = Command::new("sc")
        .args(&["delete", service::SERVICE_NAME])
        .output()?;

    if output.status.success() {
        println!("✓ Service uninstalled successfully");
    } else {
        let error = String::from_utf8_lossy(&output.stderr);
        if error.contains("does not exist") {
            println!("Service was not installed");
        } else {
            return Err(anyhow::anyhow!("Failed to uninstall service: {}", error));
        }
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = ProxyConfig::default();
        assert!(config.http.enabled);
        assert!(!config.https.enabled);
        assert!(!config.auth.enabled);
        assert_eq!(config.http.port, 8080);
        assert_eq!(config.https.port, 8443);
    }
}
