mod auth;
mod config;
mod performance;
mod proxy;
mod tls;

use anyhow::Result;
use config::ProxyConfig;
use log::{error, info};
use performance::PerformanceManager;
use proxy::ProxyServer;
use std::env;
use std::sync::Arc;
use tokio::signal;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();

    info!("Starting Rust Forward Proxy Server");

    // Load configuration
    let config_path = env::args()
        .nth(1)
        .unwrap_or_else(|| "proxy.ini".to_string());

    let config = if std::path::Path::new(&config_path).exists() {
        info!("Loading configuration from: {}", config_path);
        match ProxyConfig::from_file(&config_path) {
            Ok(cfg) => {
                info!("Configuration loaded successfully");
                cfg
            },
            Err(e) => {
                error!("Failed to load config from {}: {}", config_path, e);
                return Err(e);
            }
        }
    } else {
        info!("Config file {} not found, using default configuration", config_path);
        ProxyConfig::default()
    };

    // Validate configuration
    info!("Validating configuration...");
    match config.validate() {
        Ok(()) => info!("Configuration validation passed"),
        Err(e) => {
            error!("Configuration validation failed: {}", e);
            return Err(e);
        }
    }
    info!("HTTP enabled: {}, port: {}", config.http.enabled, config.http.port);
    info!("HTTPS enabled: {}, port: {}", config.https.enabled, config.https.port);
    info!("Authentication enabled: {}", config.auth.enabled);

    // Configure tokio runtime based on performance settings
    if let Some(worker_threads) = config.performance.worker_threads {
        info!("Using {} worker threads", worker_threads);
    }

    // Create performance manager
    let performance_manager = Arc::new(PerformanceManager::new(config.performance.clone()));
    
    // Note: Cache cleanup will be done periodically in a simpler way

    // Create proxy server
    let proxy_server = ProxyServer::new(config.clone());

    // Start servers
    let mut tasks = Vec::new();

    // Start HTTP server if enabled
    if proxy_server.config.http.enabled {
        let proxy_clone = proxy_server.clone();
        tasks.push(tokio::spawn(async move {
            if let Err(e) = proxy_clone.start_http_server().await {
                error!("HTTP server error: {}", e);
            }
        }));
    }

    // Start HTTPS server if enabled
    if proxy_server.config.https.enabled {
        let proxy_clone = proxy_server.clone();
        tasks.push(tokio::spawn(async move {
            if let Err(e) = proxy_clone.start_https_server().await {
                error!("HTTPS server error: {}", e);
            }
        }));
    }

    // Start statistics reporting task
    let perf_manager_clone = performance_manager.clone();
    tasks.push(tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));
        loop {
            interval.tick().await;
            let stats = perf_manager_clone.get_stats();
            info!(
                "Stats - Active: {}, Total: {}, Failed: {}, Bytes: {} MB",
                stats.active_connections,
                stats.total_connections,
                stats.failed_connections,
                stats.bytes_transferred / (1024 * 1024)
            );
        }
    }));

    if tasks.is_empty() {
        error!("No servers enabled. Please enable at least HTTP or HTTPS in the configuration.");
        return Ok(());
    }

    info!("All servers started successfully");

    // Wait for shutdown signal
    tokio::select! {
        _ = signal::ctrl_c() => {
            info!("Received Ctrl+C, shutting down...");
        }
        _ = futures::future::join_all(tasks) => {
            info!("All servers stopped");
        }
    }

    info!("Proxy server shutdown complete");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = ProxyConfig::default();
        assert!(config.http.enabled);
        assert!(!config.https.enabled);
        assert!(!config.auth.enabled);
        assert_eq!(config.http.port, 8080);
        assert_eq!(config.https.port, 8443);
    }
}
