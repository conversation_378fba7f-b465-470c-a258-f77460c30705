@echo off
echo Testing Service Path Resolution
echo ===============================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% NEQ 0 (
    echo ERROR: Administrator privileges required
    echo Please run as administrator
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

REM 构建项目
echo Building project...
cargo build --release
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

REM 创建绝对路径配置文件
echo Creating service configuration with absolute paths...
set CONFIG_FILE=%CD%\config_service.ini
echo Config file: %CONFIG_FILE%

REM 更新配置文件中的路径
powershell -Command "(Get-Content '%CONFIG_FILE%') -replace 'C:\\Service\\https代理服务器rust', '%CD%' | Set-Content '%CONFIG_FILE%'"

echo ✓ Configuration updated with current directory paths
echo.

REM 停止现有服务
echo Stopping existing service...
sc stop RustForwardProxy >nul 2>&1
timeout /t 3 /nobreak >nul

REM 删除现有服务
echo Removing existing service...
sc delete RustForwardProxy >nul 2>&1
timeout /t 2 /nobreak >nul

REM 创建新服务
echo Creating service...
sc create RustForwardProxy binPath= "\"%CD%\target\release\rust-forward-proxy.exe\" --service" DisplayName= "Rust Forward Proxy" start= auto type= own

if %ERRORLEVEL% EQU 0 (
    echo ✓ Service created successfully
    
    REM 设置描述
    sc description RustForwardProxy "High-performance forward proxy server with HTTP/HTTPS support"
    
    echo.
    echo Starting service...
    sc start RustForwardProxy
    
    if %ERRORLEVEL% EQU 0 (
        echo ✓ Service started successfully
        echo.
        echo Checking service status...
        sc query RustForwardProxy
        echo.
        echo Checking ports (should see 2000 and 2443)...
        netstat -an | findstr ":2000\|:2443"
        echo.
        echo Service logs can be viewed in Event Viewer:
        echo   Windows Logs > Application
        echo   Look for "RustForwardProxy" events
    ) else (
        echo ✗ Service failed to start
        echo Check Event Viewer for error details
    )
    
) else (
    echo ✗ Service creation failed
)

echo.
echo Service management commands:
echo   sc query RustForwardProxy    - Check status
echo   sc stop RustForwardProxy     - Stop service
echo   sc start RustForwardProxy    - Start service
echo   sc delete RustForwardProxy   - Remove service

echo.
pause
